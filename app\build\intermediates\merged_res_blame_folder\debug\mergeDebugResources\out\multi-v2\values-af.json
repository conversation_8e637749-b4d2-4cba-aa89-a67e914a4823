{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-44:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\006d66efee5949fce671fa82148beb5e\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,403,501,608,717,8270", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "198,300,398,496,603,712,832,8366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f89340b73105be9e288b82a683d2dd80\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,993,1074,1145,1220,1291,1362,1443,1513", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,988,1069,1140,1215,1286,1357,1438,1508,1628"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "837,933,1020,1117,1218,1304,1380,7647,7737,7823,7901,7982,8053,8128,8199,8371,8452,8522", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,74,70,70,80,69,119", "endOffsets": "928,1015,1112,1213,1299,1375,1466,7732,7818,7896,7977,8048,8123,8194,8265,8447,8517,8637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dd3f9e26b3a95f906c09105528548076\\transformed\\material3-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4612,4697,4785,4890,4971,5054,5153,5251,5346,5444,5532,5635,5735,5838,5954,6035,6135", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4607,4692,4780,4885,4966,5049,5148,5246,5341,5439,5527,5630,5730,5833,5949,6030,6130,6226"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1471,1588,1706,1821,1937,2037,2141,2262,2403,2531,2673,2758,2857,2947,3043,3158,3279,3383,3511,3636,3768,3934,4059,4181,4304,4433,4524,4623,4739,4865,4965,5075,5178,5315,5455,5561,5659,5736,5830,5924,6028,6113,6201,6306,6387,6470,6569,6667,6762,6860,6948,7051,7151,7254,7370,7451,7551", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "1583,1701,1816,1932,2032,2136,2257,2398,2526,2668,2753,2852,2942,3038,3153,3274,3378,3506,3631,3763,3929,4054,4176,4299,4428,4519,4618,4734,4860,4960,5070,5173,5310,5450,5556,5654,5731,5825,5919,6023,6108,6196,6301,6382,6465,6564,6662,6757,6855,6943,7046,7146,7249,7365,7446,7546,7642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7fb22f1576da75cb6d2a91da2bf9c0\\transformed\\foundation-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,84", "endOffsets": "137,222"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8642,8729", "endColumns": "86,84", "endOffsets": "8724,8809"}}]}]}