  Activity android.app  Greeting android.app.Activity  Modifier android.app.Activity  MyApplicationTheme android.app.Activity  Scaffold android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  Context android.content  Greeting android.content.Context  Modifier android.content.Context  MyApplicationTheme android.content.Context  Scaffold android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  
setContent android.content.Context  Greeting android.content.ContextWrapper  Modifier android.content.ContextWrapper  MyApplicationTheme android.content.ContextWrapper  Scaffold android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Greeting  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  MyApplicationTheme  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Greeting #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  MyApplicationTheme #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  
PaddingValues "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  Scaffold androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Preview #androidx.compose.ui.tooling.preview  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  Greeting #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  MyApplicationTheme #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Bundle com.example.myapplication  ComponentActivity com.example.myapplication  
Composable com.example.myapplication  Greeting com.example.myapplication  GreetingPreview com.example.myapplication  MainActivity com.example.myapplication  Modifier com.example.myapplication  MyApplicationTheme com.example.myapplication  Preview com.example.myapplication  Scaffold com.example.myapplication  String com.example.myapplication  fillMaxSize com.example.myapplication  padding com.example.myapplication  Greeting &com.example.myapplication.MainActivity  Modifier &com.example.myapplication.MainActivity  MyApplicationTheme &com.example.myapplication.MainActivity  Scaffold &com.example.myapplication.MainActivity  enableEdgeToEdge &com.example.myapplication.MainActivity  fillMaxSize &com.example.myapplication.MainActivity  padding &com.example.myapplication.MainActivity  
setContent &com.example.myapplication.MainActivity  Boolean "com.example.myapplication.ui.theme  Build "com.example.myapplication.ui.theme  
Composable "com.example.myapplication.ui.theme  DarkColorScheme "com.example.myapplication.ui.theme  
FontFamily "com.example.myapplication.ui.theme  
FontWeight "com.example.myapplication.ui.theme  LightColorScheme "com.example.myapplication.ui.theme  MyApplicationTheme "com.example.myapplication.ui.theme  Pink40 "com.example.myapplication.ui.theme  Pink80 "com.example.myapplication.ui.theme  Purple40 "com.example.myapplication.ui.theme  Purple80 "com.example.myapplication.ui.theme  PurpleGrey40 "com.example.myapplication.ui.theme  PurpleGrey80 "com.example.myapplication.ui.theme  
Typography "com.example.myapplication.ui.theme  Unit "com.example.myapplication.ui.theme  sp 
kotlin.Double  	compareTo 
kotlin.Int                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 