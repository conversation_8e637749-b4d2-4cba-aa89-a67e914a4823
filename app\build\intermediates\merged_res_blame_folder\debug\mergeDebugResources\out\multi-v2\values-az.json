{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-44:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\006d66efee5949fce671fa82148beb5e\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,206,308,411,515,616,721,8375", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "201,303,406,510,611,716,827,8471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\dd3f9e26b3a95f906c09105528548076\\transformed\\material3-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,183,305,418,543,645,747,866,1002,1123,1269,1354,1455,1546,1644,1756,1878,1984,2123,2260,2390,2549,2674,2789,2907,3023,3115,3214,3331,3463,3568,3673,3779,3917,4060,4170,4271,4347,4450,4550,4673,4761,4850,4955,5035,5119,5219,5319,5416,5514,5602,5706,5806,5908,6026,6106,6215", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "178,300,413,538,640,742,861,997,1118,1264,1349,1450,1541,1639,1751,1873,1979,2118,2255,2385,2544,2669,2784,2902,3018,3110,3209,3326,3458,3563,3668,3774,3912,4055,4165,4266,4342,4445,4545,4668,4756,4845,4950,5030,5114,5214,5314,5411,5509,5597,5701,5801,5903,6021,6101,6210,6308"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1478,1606,1728,1841,1966,2068,2170,2289,2425,2546,2692,2777,2878,2969,3067,3179,3301,3407,3546,3683,3813,3972,4097,4212,4330,4446,4538,4637,4754,4886,4991,5096,5202,5340,5483,5593,5694,5770,5873,5973,6096,6184,6273,6378,6458,6542,6642,6742,6839,6937,7025,7129,7229,7331,7449,7529,7638", "endColumns": "127,121,112,124,101,101,118,135,120,145,84,100,90,97,111,121,105,138,136,129,158,124,114,117,115,91,98,116,131,104,104,105,137,142,109,100,75,102,99,122,87,88,104,79,83,99,99,96,97,87,103,99,101,117,79,108,97", "endOffsets": "1601,1723,1836,1961,2063,2165,2284,2420,2541,2687,2772,2873,2964,3062,3174,3296,3402,3541,3678,3808,3967,4092,4207,4325,4441,4533,4632,4749,4881,4986,5091,5197,5335,5478,5588,5689,5765,5868,5968,6091,6179,6268,6373,6453,6537,6637,6737,6834,6932,7020,7124,7224,7326,7444,7524,7633,7731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cf7fb22f1576da75cb6d2a91da2bf9c0\\transformed\\foundation-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,93", "endOffsets": "139,233"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8740,8829", "endColumns": "88,93", "endOffsets": "8824,8918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f89340b73105be9e288b82a683d2dd80\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,1006,1095,1167,1241,1317,1390,1471,1537", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,1001,1090,1162,1236,1312,1385,1466,1532,1649"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "832,932,1020,1117,1218,1309,1390,7736,7828,7910,7991,8080,8152,8226,8302,8476,8557,8623", "endColumns": "99,87,96,100,90,80,87,91,81,80,88,71,73,75,72,80,65,116", "endOffsets": "927,1015,1112,1213,1304,1385,1473,7823,7905,7986,8075,8147,8221,8297,8370,8552,8618,8735"}}]}]}